/**
 * Comprehensive Image Gallery Stacking Fix
 * Prevents images from stacking on top of each other before Slick carousel initialization
 * Author: Farmart Theme Fix
 * Version: 1.0
 */

(function($) {
    'use strict';

    // Gallery stacking fix object
    const GalleryStackingFix = {
        
        // Configuration
        config: {
            selectors: {
                productGallery: '.bb-product-gallery-images',
                quickViewGallery: '.bb-quick-view-gallery-images',
                allGalleries: '.bb-product-gallery-images, .bb-quick-view-gallery-images'
            },
            delays: {
                initial: 50,
                afterSlick: 100,
                afterChange: 50,
                resize: 100,
                modal: 100
            }
        },

        // Initialize the fix
        init: function() {
            console.log('Initializing Gallery Stacking Fix');
            
            // Run initial fix immediately
            this.fixAllGalleries();
            
            // Set up observers and event handlers
            this.setupMutationObserver();
            this.setupEventHandlers();
            
            // Run fixes at different intervals to catch all scenarios
            this.scheduleDelayedFixes();
        },

        // Main function to fix image stacking issues
        fixImageStacking: function($gallery, galleryType) {
            if (!$gallery || !$gallery.length) return;

            const isInitialized = $gallery.hasClass('slick-initialized');
            
            console.log(`Fixing ${galleryType} gallery stacking - Slick initialized: ${isInitialized}`);

            if (!isInitialized) {
                // Before slick initialization - hide all except first
                this.hideNonFirstImages($gallery);
                this.showFirstImage($gallery);
                
                // Mark gallery as ready
                $gallery.addClass('gallery-ready');
            } else {
                // After slick initialization - fix all slides
                this.fixSlickSlides($gallery);
            }
        },

        // Hide all images except the first one
        hideNonFirstImages: function($gallery) {
            $gallery.find('> a:not(:first-child), > div:not(:first-child)').css({
                'display': 'none',
                'position': 'absolute',
                'left': '-9999px',
                'top': '-9999px',
                'opacity': '0',
                'visibility': 'hidden',
                'z-index': '-1'
            });
        },

        // Ensure first image is visible
        showFirstImage: function($gallery) {
            $gallery.find('> a:first-child, > div:first-child').css({
                'display': 'block',
                'position': 'relative',
                'left': 'auto',
                'top': 'auto',
                'opacity': '1',
                'visibility': 'visible',
                'z-index': '1'
            });
        },

        // Fix Slick slides after initialization
        fixSlickSlides: function($gallery) {
            // Fix slides
            $gallery.find('.slick-slide').css({
                'display': 'block',
                'position': 'relative',
                'opacity': '1',
                'visibility': 'visible',
                'z-index': '1'
            });

            // Fix slick track
            $gallery.find('.slick-track').css({
                'display': 'flex',
                'align-items': 'center',
                'position': 'relative'
            });

            // Fix individual slides
            $gallery.find('.slick-slide').css({
                'height': 'auto',
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center',
                'position': 'relative'
            });

            // Fix slide content
            $gallery.find('.slick-slide > a, .slick-slide > div').css({
                'width': '100%',
                'height': 'auto',
                'display': 'block',
                'position': 'relative'
            });

            // Fix images
            $gallery.find('img').css({
                'width': '100%',
                'height': 'auto',
                'object-fit': 'contain',
                'display': 'block',
                'position': 'relative'
            });
        },

        // Fix all galleries
        fixAllGalleries: function() {
            const self = this;
            
            // Fix main product gallery
            const $productGallery = $(this.config.selectors.productGallery);
            if ($productGallery.length) {
                this.fixImageStacking($productGallery, 'product');
            }

            // Fix quick view gallery
            const $quickViewGallery = $(this.config.selectors.quickViewGallery);
            if ($quickViewGallery.length) {
                this.fixImageStacking($quickViewGallery, 'quick-view');
            }

            // Fix any other gallery types
            const $otherGalleries = $('.product-gallery--with-images .bb-product-gallery-images');
            if ($otherGalleries.length) {
                $otherGalleries.each(function() {
                    self.fixImageStacking($(this), 'product-with-images');
                });
            }
        },

        // Set up mutation observer to watch for class changes
        setupMutationObserver: function() {
            const self = this;
            
            if (typeof MutationObserver === 'undefined') return;

            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const $target = $(mutation.target);
                        if ($target.is(self.config.selectors.allGalleries)) {
                            console.log('Gallery class changed, fixing stacking');
                            setTimeout(function() {
                                const galleryType = $target.hasClass('bb-quick-view-gallery-images') ? 'quick-view' : 'product';
                                self.fixImageStacking($target, galleryType);
                            }, self.config.delays.afterSlick);
                        }
                    }
                });
            });

            // Observe all gallery elements
            $(this.config.selectors.allGalleries).each(function() {
                observer.observe(this, {
                    attributes: true,
                    attributeFilter: ['class']
                });
            });
        },

        // Set up event handlers
        setupEventHandlers: function() {
            const self = this;

            // Fix when Slick events occur
            $(document).on('init', this.config.selectors.allGalleries, function() {
                const $gallery = $(this);
                const galleryType = $gallery.hasClass('bb-quick-view-gallery-images') ? 'quick-view' : 'product';
                setTimeout(function() {
                    self.fixImageStacking($gallery, galleryType);
                }, self.config.delays.afterSlick);
            });

            $(document).on('afterChange', this.config.selectors.allGalleries, function() {
                const $gallery = $(this);
                const galleryType = $gallery.hasClass('bb-quick-view-gallery-images') ? 'quick-view' : 'product';
                setTimeout(function() {
                    self.fixImageStacking($gallery, galleryType);
                }, self.config.delays.afterChange);
            });

            // Fix when modals are shown (for quick view)
            $(document).on('shown.bs.modal', function() {
                setTimeout(function() {
                    self.fixAllGalleries();
                }, self.config.delays.modal);
            });

            // Fix on window resize
            $(window).on('resize', function() {
                setTimeout(function() {
                    self.fixAllGalleries();
                }, self.config.delays.resize);
            });
        },

        // Schedule delayed fixes to catch all scenarios
        scheduleDelayedFixes: function() {
            const self = this;
            
            // Multiple fixes at different intervals
            setTimeout(function() { self.fixAllGalleries(); }, 100);
            setTimeout(function() { self.fixAllGalleries(); }, 500);
            setTimeout(function() { self.fixAllGalleries(); }, 1000);
            setTimeout(function() { self.fixAllGalleries(); }, 2000);
        }
    };

    // Initialize when DOM is ready
    $(document).ready(function() {
        GalleryStackingFix.init();
    });

    // Also initialize when window loads (fallback)
    $(window).on('load', function() {
        setTimeout(function() {
            GalleryStackingFix.fixAllGalleries();
        }, 100);
    });

    // Expose to global scope for manual triggering if needed
    window.GalleryStackingFix = GalleryStackingFix;

})(jQuery);
