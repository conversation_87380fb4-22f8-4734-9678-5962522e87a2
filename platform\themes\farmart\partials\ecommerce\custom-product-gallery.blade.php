@include(EcommerceHelper::viewPath('includes.product-gallery'))

<style>
    /* Custom Navigation Arrows with 15% opacity */
    .bb-product-gallery-wrapper {
        position: relative;
    }

    .custom-gallery-nav {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(255, 255, 255, 0.15);
        color: #333;
        border: none;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        cursor: pointer;
        z-index: 10;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
        transition: all 0.3s ease;
        opacity: 0;
        visibility: hidden;
    }

    .bb-product-gallery-wrapper:hover .custom-gallery-nav {
        opacity: 1;
        visibility: visible;
    }

    .custom-gallery-nav:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-50%) scale(1.1);
    }

    .custom-gallery-prev {
        left: 15px;
    }

    .custom-gallery-next {
        right: 15px;
    }

    /* Show navigation on mobile always */
    @media (max-width: 991px) {
        .custom-gallery-nav {
            opacity: 1;
            visibility: visible;
            background-color: rgba(255, 255, 255, 0.15);
            border: none;
            color: #333;
            width: 50px;
            height: 50px;
            font-size: 28px;
            font-weight: bold;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .custom-gallery-nav:hover {
            background-color: rgba(255, 255, 255, 0.25);
            transform: translateY(-50%) scale(1.1);
        }

        .custom-gallery-nav:active {
            transform: translateY(-50%) scale(0.95);
        }

        .custom-gallery-prev {
            left: 15px;
        }

        .custom-gallery-next {
            right: 15px;
        }
    }

    /* Custom Counter */
    .custom-gallery-counter {
        position: absolute;
        bottom: 20px;
        right: 20px;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        z-index: 10;
        font-weight: 500;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add custom navigation and counter to the original gallery
    const galleryWrapper = document.querySelector('.bb-product-gallery-wrapper');
    if (galleryWrapper) {
        // Add navigation arrows
        const totalImages = document.querySelectorAll('.bb-product-gallery-images > a, .bb-product-gallery-images > div').length;

        if (totalImages > 1) {
            // Add navigation buttons
            const prevBtn = document.createElement('button');
            prevBtn.className = 'custom-gallery-nav custom-gallery-prev';
            prevBtn.innerHTML = '‹';
            prevBtn.onclick = function() {
                $('.bb-product-gallery-images').slick('slickPrev');
            };

            const nextBtn = document.createElement('button');
            nextBtn.className = 'custom-gallery-nav custom-gallery-next';
            nextBtn.innerHTML = '›';
            nextBtn.onclick = function() {
                $('.bb-product-gallery-images').slick('slickNext');
            };

            galleryWrapper.appendChild(prevBtn);
            galleryWrapper.appendChild(nextBtn);

            // Add counter
            const counter = document.createElement('div');
            counter.className = 'custom-gallery-counter';
            counter.innerHTML = '<span class="current-image">1</span>/<span class="total-images">' + totalImages + '</span>';
            galleryWrapper.appendChild(counter);

            // Update counter when slide changes
            setTimeout(function() {
                $('.bb-product-gallery-images').on('afterChange', function(event, slick, currentSlide) {
                    document.querySelector('.current-image').textContent = currentSlide + 1;
                });
            }, 1000);
        }
    }

    // Initialize gallery if EcommerceApp is available
    if (typeof EcommerceApp !== 'undefined') {
        EcommerceApp.initProductGallery();
    }
});
</script>
