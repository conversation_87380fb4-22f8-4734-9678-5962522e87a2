/* 
 * Comprehensive Image Gallery Stacking Fix
 * Prevents images from stacking on top of each other before Slick carousel initialization
 * Author: Farmart Theme Fix
 * Version: 1.0
 */

/* ==========================================================================
   MAIN PRODUCT GALLERY FIXES
   ========================================================================== */

/* Core gallery container */
.bb-product-gallery-images,
.bb-quick-view-gallery-images {
    position: relative !important;
    overflow: hidden !important;
    width: 100% !important;
    height: auto !important;
}

/* CRITICAL: Hide all images except first before Slick initialization */
.bb-product-gallery-images:not(.slick-initialized) > a:not(:first-child),
.bb-product-gallery-images:not(.slick-initialized) > div:not(:first-child),
.bb-quick-view-gallery-images:not(.slick-initialized) > a:not(:first-child),
.bb-quick-view-gallery-images:not(.slick-initialized) > div:not(:first-child) {
    display: none !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    opacity: 0 !important;
    visibility: hidden !important;
    z-index: -1 !important;
}

/* Ensure first image is visible before Slick initialization */
.bb-product-gallery-images:not(.slick-initialized) > a:first-child,
.bb-product-gallery-images:not(.slick-initialized) > div:first-child,
.bb-quick-view-gallery-images:not(.slick-initialized) > a:first-child,
.bb-quick-view-gallery-images:not(.slick-initialized) > div:first-child {
    display: block !important;
    position: relative !important;
    left: auto !important;
    top: auto !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 1 !important;
}

/* ==========================================================================
   AFTER SLICK INITIALIZATION FIXES
   ========================================================================== */

/* After Slick initialization - ensure proper display */
.bb-product-gallery-images.slick-initialized .slick-slide,
.bb-quick-view-gallery-images.slick-initialized .slick-slide {
    display: block !important;
    position: relative !important;
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 1 !important;
}

/* Slick track positioning */
.bb-product-gallery-images .slick-track,
.bb-quick-view-gallery-images .slick-track {
    display: flex !important;
    align-items: center !important;
    position: relative !important;
}

/* Individual slide styling */
.bb-product-gallery-images .slick-slide,
.bb-quick-view-gallery-images .slick-slide {
    height: auto !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
}

/* Image container within slides */
.bb-product-gallery-images .slick-slide > a,
.bb-product-gallery-images .slick-slide > div,
.bb-quick-view-gallery-images .slick-slide > a,
.bb-quick-view-gallery-images .slick-slide > div {
    width: 100% !important;
    height: auto !important;
    display: block !important;
    position: relative !important;
}

/* Image styling */
.bb-product-gallery-images img,
.bb-quick-view-gallery-images img {
    width: 100% !important;
    height: auto !important;
    object-fit: contain !important;
    display: block !important;
    position: relative !important;
}

/* ==========================================================================
   ADDITIONAL FIXES FOR EDGE CASES
   ========================================================================== */

/* Remove any transforms that might cause stacking */
.bb-product-gallery-images > *,
.bb-quick-view-gallery-images > * {
    transform: none !important;
}

.bb-product-gallery-images .slick-slide > *,
.bb-quick-view-gallery-images .slick-slide > * {
    transform: none !important;
}

/* Ensure proper z-index for gallery elements */
.bb-product-gallery-images,
.bb-quick-view-gallery-images {
    z-index: 1 !important;
}

.bb-product-gallery-images .slick-slide,
.bb-quick-view-gallery-images .slick-slide {
    z-index: 1 !important;
}

/* ==========================================================================
   PRODUCT GALLERY WITH IMAGES CLASS FIXES
   ========================================================================== */

/* Additional fixes for specific gallery types */
.product-gallery--with-images .bb-product-gallery-images:not(.slick-initialized) > a:not(:first-child) {
    display: none !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

.product-gallery--with-images .bb-product-gallery-images:not(.slick-initialized) > a:first-child {
    display: block !important;
    position: relative !important;
    left: auto !important;
    top: auto !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* ==========================================================================
   MOBILE RESPONSIVE FIXES
   ========================================================================== */

@media (max-width: 768px) {
    .bb-product-gallery-images:not(.slick-initialized) > a:not(:first-child),
    .bb-product-gallery-images:not(.slick-initialized) > div:not(:first-child),
    .bb-quick-view-gallery-images:not(.slick-initialized) > a:not(:first-child),
    .bb-quick-view-gallery-images:not(.slick-initialized) > div:not(:first-child) {
        display: none !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    .bb-product-gallery-images:not(.slick-initialized) > a:first-child,
    .bb-product-gallery-images:not(.slick-initialized) > div:first-child,
    .bb-quick-view-gallery-images:not(.slick-initialized) > a:first-child,
    .bb-quick-view-gallery-images:not(.slick-initialized) > div:first-child {
        display: block !important;
        position: relative !important;
        left: auto !important;
        top: auto !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
}

/* ==========================================================================
   LOADING STATE FIXES
   ========================================================================== */

/* Hide gallery during initial load to prevent flash of stacked images */
.bb-product-gallery-images:not(.slick-initialized):not(.gallery-ready),
.bb-quick-view-gallery-images:not(.slick-initialized):not(.gallery-ready) {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bb-product-gallery-images.gallery-ready,
.bb-quick-view-gallery-images.gallery-ready {
    opacity: 1;
}

/* ==========================================================================
   FALLBACK FIXES
   ========================================================================== */

/* Fallback for any remaining stacking issues */
.bb-product-gallery-images:not(.slick-initialized) {
    display: block !important;
}

.bb-product-gallery-images:not(.slick-initialized) > *:not(:first-child) {
    display: none !important;
}

.bb-quick-view-gallery-images:not(.slick-initialized) {
    display: block !important;
}

.bb-quick-view-gallery-images:not(.slick-initialized) > *:not(:first-child) {
    display: none !important;
}
