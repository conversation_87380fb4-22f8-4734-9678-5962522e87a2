@php
    // Count total images
    $totalImages = count($productImages);
@endphp

<div class="bb-quick-view-gallery-images position-relative">
    @foreach ($productImages as $image)
        <a href="{{ RvMedia::getImageUrl($image) }}">
            {{ RvMedia::image($image, $product->name, 'medium') }}
        </a>
    @endforeach

    <!-- Image Counter -->
    <div class="image-counter">
        <span class="current-image">1</span>/<span class="total-images">{{ $totalImages }}</span>
    </div>

    <!-- Navigation Arrows -->
    <div class="gallery-nav gallery-nav-prev">
        <button class="btn-gallery-nav btn-prev">
            <span class="svg-icon">
                <svg>
                    <use href="#svg-icon-chevron-left" xlink:href="#svg-icon-chevron-left"></use>
                </svg>
            </span>
        </button>
    </div>
    <div class="gallery-nav gallery-nav-next">
        <button class="btn-gallery-nav btn-next">
            <span class="svg-icon">
                <svg>
                    <use href="#svg-icon-chevron-right" xlink:href="#svg-icon-chevron-right"></use>
                </svg>
            </span>
        </button>
    </div>
</div>

<style>
    /* Fix image stacking issues - Core quick-view gallery container */
    .bb-quick-view-gallery-images {
        position: relative !important;
        overflow: hidden !important;
        width: 100% !important;
        height: auto !important;
    }

    /* CRITICAL: Hide all images except first before Slick initialization */
    .bb-quick-view-gallery-images:not(.slick-initialized) > a:not(:first-child),
    .bb-quick-view-gallery-images:not(.slick-initialized) > div:not(:first-child) {
        display: none !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        opacity: 0 !important;
        visibility: hidden !important;
        z-index: -1 !important;
    }

    /* Ensure first image is visible before Slick initialization */
    .bb-quick-view-gallery-images:not(.slick-initialized) > a:first-child,
    .bb-quick-view-gallery-images:not(.slick-initialized) > div:first-child {
        display: block !important;
        position: relative !important;
        left: auto !important;
        top: auto !important;
        opacity: 1 !important;
        visibility: visible !important;
        z-index: 1 !important;
    }

    /* After Slick initialization - ensure proper display */
    .bb-quick-view-gallery-images.slick-initialized .slick-slide {
        display: block !important;
        position: relative !important;
        opacity: 1 !important;
        visibility: visible !important;
        z-index: 1 !important;
    }

    /* Slick track positioning */
    .bb-quick-view-gallery-images .slick-track {
        display: flex !important;
        align-items: center !important;
        position: relative !important;
    }

    /* Individual slide styling */
    .bb-quick-view-gallery-images .slick-slide {
        height: auto !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;
    }

    /* Image container within slides */
    .bb-quick-view-gallery-images .slick-slide > a,
    .bb-quick-view-gallery-images .slick-slide > div {
        width: 100% !important;
        height: auto !important;
        display: block !important;
        position: relative !important;
    }

    /* Image styling */
    .bb-quick-view-gallery-images img {
        width: 100% !important;
        height: auto !important;
        object-fit: contain !important;
        display: block !important;
        position: relative !important;
    }

    /* Image Counter Styles */
    .image-counter {
        position: absolute !important;
        bottom: 15px !important;
        right: 15px !important;
        background-color: rgba(0, 0, 0, 0.6) !important;
        color: #fff !important;
        padding: 5px 10px !important;
        border-radius: 15px !important;
        font-size: 12px !important;
        z-index: 999 !important;
        display: block !important;
    }

    /* Navigation Arrows Styles */
    .gallery-nav {
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 999 !important;
        display: block !important;
    }

    .gallery-nav-prev {
        left: 10px !important;
    }

    .gallery-nav-next {
        right: 10px !important;
    }

    .btn-gallery-nav {
        background-color: rgba(255, 255, 255, 0.7) !important;
        border: none !important;
        border-radius: 50% !important;
        width: 40px !important;
        height: 40px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .btn-gallery-nav:hover {
        background-color: rgba(255, 255, 255, 0.9) !important;
    }

    .btn-gallery-nav .svg-icon {
        width: 20px !important;
        height: 20px !important;
    }

    /* Additional fixes for edge cases */
    .bb-quick-view-gallery-images > * {
        transform: none !important;
    }

    .bb-quick-view-gallery-images .slick-slide > * {
        transform: none !important;
    }

    /* Mobile specific fixes */
    @media (max-width: 768px) {
        .bb-quick-view-gallery-images:not(.slick-initialized) > a:not(:first-child),
        .bb-quick-view-gallery-images:not(.slick-initialized) > div:not(:first-child) {
            display: none !important;
            position: absolute !important;
            left: -9999px !important;
            top: -9999px !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to fix quick-view image stacking issues
        function fixQuickViewImageStacking() {
            const $gallery = $('.bb-quick-view-gallery-images');

            if (!$gallery.length) return;

            // Before slick initialization - ensure only first image is visible
            if (!$gallery.hasClass('slick-initialized')) {
                console.log('Fixing quick-view image stacking before Slick initialization');

                // Hide all images except the first one
                $gallery.find('> a:not(:first-child), > div:not(:first-child)').css({
                    'display': 'none',
                    'position': 'absolute',
                    'left': '-9999px',
                    'top': '-9999px',
                    'opacity': '0',
                    'visibility': 'hidden',
                    'z-index': '-1'
                });

                // Ensure first image is visible
                $gallery.find('> a:first-child, > div:first-child').css({
                    'display': 'block',
                    'position': 'relative',
                    'left': 'auto',
                    'top': 'auto',
                    'opacity': '1',
                    'visibility': 'visible',
                    'z-index': '1'
                });
            } else {
                console.log('Fixing quick-view image stacking after Slick initialization');

                // After slick initialization - ensure proper display
                $gallery.find('.slick-slide').css({
                    'display': 'block',
                    'position': 'relative',
                    'opacity': '1',
                    'visibility': 'visible',
                    'z-index': '1'
                });

                // Fix slick track
                $gallery.find('.slick-track').css({
                    'display': 'flex',
                    'align-items': 'center',
                    'position': 'relative'
                });

                // Fix individual slides
                $gallery.find('.slick-slide').css({
                    'height': 'auto',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center',
                    'position': 'relative'
                });

                // Fix slide content
                $gallery.find('.slick-slide > a, .slick-slide > div').css({
                    'width': '100%',
                    'height': 'auto',
                    'display': 'block',
                    'position': 'relative'
                });

                // Fix images
                $gallery.find('img').css({
                    'width': '100%',
                    'height': 'auto',
                    'object-fit': 'contain',
                    'display': 'block',
                    'position': 'relative'
                });
            }
        }

        // Initial fix - run immediately
        fixQuickViewImageStacking();

        // Make sure the elements are visible
        $('.image-counter, .gallery-nav').css('display', 'block');

        // Monitor for Slick initialization using MutationObserver
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const $target = $(mutation.target);
                    if ($target.hasClass('bb-quick-view-gallery-images')) {
                        console.log('Quick-view gallery class changed, fixing stacking');
                        setTimeout(fixQuickViewImageStacking, 50);
                    }
                }
            });
        });

        // Observe the gallery for class changes
        const galleryElement = document.querySelector('.bb-quick-view-gallery-images');
        if (galleryElement) {
            observer.observe(galleryElement, {
                attributes: true,
                attributeFilter: ['class']
            });
        }

        // Fix when modal is shown (for quick view)
        $(document).on('shown.bs.modal', function() {
            setTimeout(fixQuickViewImageStacking, 100);
        });

        // Wait for slick to be initialized and set up event handlers
        setTimeout(function() {
            // Fix stacking after slick initialization
            fixQuickViewImageStacking();

            // Update the current image number when the slide changes
            $('.bb-quick-view-gallery-images').on('afterChange', function(event, slick, currentSlide) {
                $('.current-image').text(currentSlide + 1);
                // Fix stacking after slide change
                setTimeout(fixQuickViewImageStacking, 50);
            });

            // Add click handlers for the navigation buttons
            $('.btn-prev').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-quick-view-gallery-images').slick('slickPrev');
                return false;
            });

            $('.btn-next').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-quick-view-gallery-images').slick('slickNext');
                return false;
            });

            // Additional fix after a longer delay
            setTimeout(fixQuickViewImageStacking, 500);
        }, 1000); // Wait 1 second for slick to initialize

        // Fix stacking on window resize
        $(window).on('resize', function() {
            setTimeout(fixQuickViewImageStacking, 100);
        });

        // Additional fixes at different intervals
        setTimeout(fixQuickViewImageStacking, 100);
        setTimeout(fixQuickViewImageStacking, 500);
        setTimeout(fixQuickViewImageStacking, 2000);
    });
</script>
