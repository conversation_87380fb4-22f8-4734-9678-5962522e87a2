<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Stacking Fix Test</title>
    <link rel="stylesheet" href="css/gallery-stacking-fix.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-gallery {
            margin-bottom: 40px;
        }
        .test-gallery h3 {
            margin-bottom: 20px;
            color: #333;
        }
        .bb-product-gallery-images,
        .bb-quick-view-gallery-images {
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            background: #f9f9f9;
            min-height: 300px;
        }
        .bb-product-gallery-images img,
        .bb-quick-view-gallery-images img {
            max-width: 100%;
            height: auto;
            display: block;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .init-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .init-button:hover {
            background: #005a87;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Gallery Stacking Fix Test</h1>
        
        <div class="test-info">
            <strong>Test Instructions:</strong>
            <ol>
                <li>Before clicking "Initialize Slick", you should see only the first image in each gallery</li>
                <li>Other images should be hidden to prevent stacking</li>
                <li>After clicking "Initialize Slick", the carousel should work normally</li>
                <li>Check the browser console for debug messages</li>
            </ol>
        </div>

        <div class="test-gallery">
            <h3>Product Gallery Test</h3>
            <div class="bb-product-gallery-images">
                <a href="#"><img src="https://via.placeholder.com/400x300/ff6b6b/ffffff?text=Image+1" alt="Test Image 1"></a>
                <a href="#"><img src="https://via.placeholder.com/400x300/4ecdc4/ffffff?text=Image+2" alt="Test Image 2"></a>
                <a href="#"><img src="https://via.placeholder.com/400x300/45b7d1/ffffff?text=Image+3" alt="Test Image 3"></a>
                <a href="#"><img src="https://via.placeholder.com/400x300/f9ca24/ffffff?text=Image+4" alt="Test Image 4"></a>
            </div>
            <button class="init-button" onclick="initProductGallery()">Initialize Product Gallery Slick</button>
            <div id="product-status" class="status"></div>
        </div>

        <div class="test-gallery">
            <h3>Quick View Gallery Test</h3>
            <div class="bb-quick-view-gallery-images">
                <a href="#"><img src="https://via.placeholder.com/400x300/6c5ce7/ffffff?text=QV+Image+1" alt="Quick View Image 1"></a>
                <a href="#"><img src="https://via.placeholder.com/400x300/a29bfe/ffffff?text=QV+Image+2" alt="Quick View Image 2"></a>
                <a href="#"><img src="https://via.placeholder.com/400x300/fd79a8/ffffff?text=QV+Image+3" alt="Quick View Image 3"></a>
            </div>
            <button class="init-button" onclick="initQuickViewGallery()">Initialize Quick View Gallery Slick</button>
            <div id="quickview-status" class="status"></div>
        </div>

        <div class="test-info">
            <strong>Expected Behavior:</strong>
            <ul>
                <li>✅ Only first image visible initially</li>
                <li>✅ No image stacking/overlapping</li>
                <li>✅ Smooth transition to Slick carousel</li>
                <li>✅ All images accessible after Slick initialization</li>
            </ul>
        </div>
    </div>

    <script src="js/gallery-stacking-fix.js"></script>
    <script>
        function updateStatus(elementId, message, isSuccess) {
            const statusEl = document.getElementById(elementId);
            statusEl.textContent = message;
            statusEl.className = 'status ' + (isSuccess ? 'success' : 'error');
        }

        function initProductGallery() {
            try {
                $('.bb-product-gallery-images').slick({
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    infinite: false,
                    dots: true,
                    arrows: true,
                    lazyLoad: 'ondemand'
                });
                updateStatus('product-status', 'Product gallery Slick initialized successfully!', true);
            } catch (error) {
                updateStatus('product-status', 'Error initializing product gallery: ' + error.message, false);
            }
        }

        function initQuickViewGallery() {
            try {
                $('.bb-quick-view-gallery-images').slick({
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    infinite: false,
                    dots: true,
                    arrows: true,
                    lazyLoad: 'ondemand'
                });
                updateStatus('quickview-status', 'Quick view gallery Slick initialized successfully!', true);
            } catch (error) {
                updateStatus('quickview-status', 'Error initializing quick view gallery: ' + error.message, false);
            }
        }

        // Test the fix on page load
        $(document).ready(function() {
            console.log('Test page loaded - checking initial state');
            
            // Check if only first images are visible
            const productImages = $('.bb-product-gallery-images > a');
            const quickViewImages = $('.bb-quick-view-gallery-images > a');
            
            console.log('Product gallery images:', productImages.length);
            console.log('Quick view gallery images:', quickViewImages.length);
            
            // Log visibility of each image
            productImages.each(function(index) {
                const isVisible = $(this).is(':visible');
                console.log(`Product image ${index + 1} visible:`, isVisible);
            });
            
            quickViewImages.each(function(index) {
                const isVisible = $(this).is(':visible');
                console.log(`Quick view image ${index + 1} visible:`, isVisible);
            });
        });
    </script>
</body>
</html>
