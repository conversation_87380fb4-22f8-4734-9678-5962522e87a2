@php
    EcommerceHelper::registerThemeAssets();
    $version = get_cms_version();
    Theme::asset()->add('lightgallery-css', 'vendor/core/plugins/ecommerce/libraries/lightgallery/css/lightgallery.min.css', version: $version);
    Theme::asset()->add('slick-css', 'vendor/core/plugins/ecommerce/libraries/slick/slick.css', version: $version);
    Theme::asset()->container('footer')->add('lightgallery-js', 'vendor/core/plugins/ecommerce/libraries/lightgallery/js/lightgallery.min.js', ['jquery'], version: $version);
    Theme::asset()->container('footer')->add('slick-js', 'vendor/core/plugins/ecommerce/libraries/slick/slick.min.js', ['jquery'], version: $version);

    $galleryStyle = theme_option('ecommerce_product_gallery_image_style', 'vertical');

    // Count total images and videos
    $totalImages = count($productImages);
    $totalVideos = 0;
    foreach($product->video as $video) {
        if ($video['url']) {
            $totalVideos++;
        }
    }
    $totalMedia = $totalImages + $totalVideos;
@endphp

<div class="bb-product-gallery-wrapper">
    <div @class(['bb-product-gallery', 'bb-product-gallery-' . $galleryStyle])>
        <div class="bb-product-gallery-images position-relative">
            @if (! empty($product->video))
                @foreach($product->video as $video)
                    @continue(! $video['url'])

                    <div class="bb-product-video">
                        @if ($video['provider'] === 'video')
                            <video
                                id="{{ md5($video['url']) }}"
                                playsinline="playsinline"
                                mute="true"
                                preload="auto"
                                class="media-video"
                                aria-label="{{ $product->name }}"
                                poster="{{ $video['thumbnail'] }}"
                            >
                                <source src="{{ $video['url'] }}" type="video/{{ File::extension($video['url']) ?: 'mp4' }}">
                                <img src="{{ $video['thumbnail'] }}" alt="{{ $video['url'] }}">
                            </video>
                            <button class="bb-button-trigger-play-video" data-target="{{ md5($video['url']) }}">
                                <x-core::icon name="ti ti-player-play-filled" />
                            </button>
                        @else
                            <iframe
                                data-provider="{{ $video['provider'] }}"
                                src="{{ $video['url'] }}"
                                frameborder="0"
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen>
                            </iframe>
                        @endif
                    </div>
                @endforeach
            @endif
            @foreach ($productImages as $image)
                <a href="{{ RvMedia::getImageUrl($image) }}">
                    {{ RvMedia::image($image, $product->name) }}
                </a>
            @endforeach

            <!-- Image Counter -->
            <div class="image-counter">
                <span class="current-image">1</span>/<span class="total-images">{{ $totalMedia }}</span>
            </div>

            <!-- Navigation Arrows -->
            <div class="gallery-nav gallery-nav-prev">
                <button class="btn-gallery-nav btn-prev">
                    <span class="svg-icon">
                        <svg>
                            <use href="#svg-icon-chevron-left" xlink:href="#svg-icon-chevron-left"></use>
                        </svg>
                    </span>
                </button>
            </div>
            <div class="gallery-nav gallery-nav-next">
                <button class="btn-gallery-nav btn-next">
                    <span class="svg-icon">
                        <svg>
                            <use href="#svg-icon-chevron-right" xlink:href="#svg-icon-chevron-right"></use>
                        </svg>
                    </span>
                </button>
            </div>
        </div>
        <div class="bb-product-gallery-thumbnails" data-vertical="{{ $galleryStyle === 'vertical' ? 1 : 0 }}">
            @foreach($product->video as $video)
                @continue(! $video['url'])

                <div class="video-thumbnail">
                    <img src="{{ $video['thumbnail'] }}" alt="{{ $product->name }}">
                    <x-core::icon name="ti ti-player-play-filled" />
                </div>
            @endforeach
            @foreach ($productImages as $image)
                <div>
                    {{ RvMedia::image($image, $product->name, 'thumb') }}
                </div>
            @endforeach
        </div>
    </div>
</div>

<style>
    /* Fix image stacking issues - Core gallery container */
    .bb-product-gallery-images {
        position: relative !important;
        overflow: hidden !important;
        width: 100% !important;
        height: auto !important;
    }

    /* CRITICAL: Hide all images except first before Slick initialization */
    .bb-product-gallery-images:not(.slick-initialized) > a:not(:first-child),
    .bb-product-gallery-images:not(.slick-initialized) > div:not(:first-child) {
        display: none !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
        opacity: 0 !important;
        visibility: hidden !important;
        z-index: -1 !important;
    }

    /* Ensure first image is visible before Slick initialization */
    .bb-product-gallery-images:not(.slick-initialized) > a:first-child,
    .bb-product-gallery-images:not(.slick-initialized) > div:first-child {
        display: block !important;
        position: relative !important;
        left: auto !important;
        top: auto !important;
        opacity: 1 !important;
        visibility: visible !important;
        z-index: 1 !important;
    }

    /* After Slick initialization - ensure proper display */
    .bb-product-gallery-images.slick-initialized .slick-slide {
        display: block !important;
        position: relative !important;
        opacity: 1 !important;
        visibility: visible !important;
        z-index: 1 !important;
    }

    /* Slick track positioning */
    .bb-product-gallery-images .slick-track {
        display: flex !important;
        align-items: center !important;
        position: relative !important;
    }

    /* Individual slide styling */
    .bb-product-gallery-images .slick-slide {
        height: auto !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;
    }

    /* Image container within slides */
    .bb-product-gallery-images .slick-slide > a,
    .bb-product-gallery-images .slick-slide > div {
        width: 100% !important;
        height: auto !important;
        display: block !important;
        position: relative !important;
    }

    /* Image styling */
    .bb-product-gallery-images img {
        width: 100% !important;
        height: auto !important;
        object-fit: contain !important;
        display: block !important;
        position: relative !important;
    }

    /* Image Counter Styles */
    .image-counter {
        position: absolute !important;
        bottom: 15px !important;
        right: 15px !important;
        background-color: rgba(0, 0, 0, 0.6) !important;
        color: #fff !important;
        padding: 5px 10px !important;
        border-radius: 15px !important;
        font-size: 12px !important;
        z-index: 999 !important;
        display: block !important;
    }

    /* Navigation Arrows Styles */
    .gallery-nav {
        position: absolute !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        z-index: 999 !important;
        display: block !important;
    }

    .gallery-nav-prev {
        left: 10px !important;
    }

    .gallery-nav-next {
        right: 10px !important;
    }

    .btn-gallery-nav {
        background-color: rgba(255, 255, 255, 0.7) !important;
        border: none !important;
        border-radius: 50% !important;
        width: 40px !important;
        height: 40px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .btn-gallery-nav:hover {
        background-color: rgba(255, 255, 255, 0.9) !important;
    }

    .btn-gallery-nav .svg-icon {
        width: 20px !important;
        height: 20px !important;
    }

    /* Additional fixes for edge cases */
    .bb-product-gallery-images > * {
        transform: none !important;
    }

    .bb-product-gallery-images .slick-slide > * {
        transform: none !important;
    }

    /* Mobile specific fixes */
    @media (max-width: 768px) {
        .bb-product-gallery-images:not(.slick-initialized) > a:not(:first-child),
        .bb-product-gallery-images:not(.slick-initialized) > div:not(:first-child) {
            display: none !important;
            position: absolute !important;
            left: -9999px !important;
            top: -9999px !important;
            opacity: 0 !important;
            visibility: hidden !important;
        }
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to fix image stacking issues
        function fixImageStacking() {
            const $gallery = $('.bb-product-gallery-images');

            if (!$gallery.length) return;

            // Before slick initialization - ensure only first image is visible
            if (!$gallery.hasClass('slick-initialized')) {
                console.log('Fixing image stacking before Slick initialization');

                // Hide all images except the first one
                $gallery.find('> a:not(:first-child), > div:not(:first-child)').css({
                    'display': 'none',
                    'position': 'absolute',
                    'left': '-9999px',
                    'top': '-9999px',
                    'opacity': '0',
                    'visibility': 'hidden',
                    'z-index': '-1'
                });

                // Ensure first image is visible
                $gallery.find('> a:first-child, > div:first-child').css({
                    'display': 'block',
                    'position': 'relative',
                    'left': 'auto',
                    'top': 'auto',
                    'opacity': '1',
                    'visibility': 'visible',
                    'z-index': '1'
                });
            } else {
                console.log('Fixing image stacking after Slick initialization');

                // After slick initialization - ensure proper display
                $gallery.find('.slick-slide').css({
                    'display': 'block',
                    'position': 'relative',
                    'opacity': '1',
                    'visibility': 'visible',
                    'z-index': '1'
                });

                // Fix slick track
                $gallery.find('.slick-track').css({
                    'display': 'flex',
                    'align-items': 'center',
                    'position': 'relative'
                });

                // Fix individual slides
                $gallery.find('.slick-slide').css({
                    'height': 'auto',
                    'display': 'flex',
                    'align-items': 'center',
                    'justify-content': 'center',
                    'position': 'relative'
                });

                // Fix slide content
                $gallery.find('.slick-slide > a, .slick-slide > div').css({
                    'width': '100%',
                    'height': 'auto',
                    'display': 'block',
                    'position': 'relative'
                });

                // Fix images
                $gallery.find('img').css({
                    'width': '100%',
                    'height': 'auto',
                    'object-fit': 'contain',
                    'display': 'block',
                    'position': 'relative'
                });
            }
        }

        // Initial fix - run immediately
        fixImageStacking();

        // Make sure the elements are visible
        $('.image-counter, .gallery-nav').css('display', 'block');

        // Monitor for Slick initialization using MutationObserver
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const $target = $(mutation.target);
                    if ($target.hasClass('bb-product-gallery-images')) {
                        console.log('Gallery class changed, fixing stacking');
                        setTimeout(fixImageStacking, 50);
                    }
                }
            });
        });

        // Observe the gallery for class changes
        const galleryElement = document.querySelector('.bb-product-gallery-images');
        if (galleryElement) {
            observer.observe(galleryElement, {
                attributes: true,
                attributeFilter: ['class']
            });
        }

        // Wait for slick to be initialized and set up event handlers
        setTimeout(function() {
            // Fix stacking after slick initialization
            fixImageStacking();

            // Update the current image number when the slide changes
            $('.bb-product-gallery-images').on('afterChange', function(event, slick, currentSlide) {
                $('.current-image').text(currentSlide + 1);
                // Fix stacking after slide change
                setTimeout(fixImageStacking, 50);
            });

            // Add click handlers for the navigation buttons
            $('.btn-prev').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-product-gallery-images').slick('slickPrev');
                return false;
            });

            $('.btn-next').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                $('.bb-product-gallery-images').slick('slickNext');
                return false;
            });

            // Additional fix after a longer delay
            setTimeout(fixImageStacking, 500);
        }, 1000); // Wait 1 second for slick to initialize

        // Fix stacking on window resize
        $(window).on('resize', function() {
            setTimeout(fixImageStacking, 100);
        });

        // Additional fixes at different intervals
        setTimeout(fixImageStacking, 100);
        setTimeout(fixImageStacking, 500);
        setTimeout(fixImageStacking, 2000);
    });
</script>
